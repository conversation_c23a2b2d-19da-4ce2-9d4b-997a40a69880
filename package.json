{"name": "a<PERSON>ya", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@svgr/webpack": "^8.1.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.6.2", "lucide-react": "^0.483.0", "next": "15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "sass": "^1.86.0", "scss": "^0.2.4", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.2.3", "tailwindcss": "^4"}}