// styles/sidebar/Sidebar.module.scss

.sidebar {
  width: 300px;
  height: 100vh;
  background: var(--pure-white);
  color: var(--deep-navy);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  overflow-y: auto;
  // border-right: 1px solid var(--border-gray);
  box-shadow: 0 10px 25px -5px rgba(18, 22, 34, 0.08), 0 8px 10px -6px rgba(18, 22, 34, 0.04);

  // Smooth scrollbar styling
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--border-gray);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
  }
}

.logo {
  padding: 2rem 1.5rem 1.5rem;
  border-bottom: 1px solid var(--border-gray);
  background: linear-gradient(135deg, var(--background-light) 0%, var(--pure-white) 100%);
  display: flex;
  align-items: center;
  gap: 1rem;

  .logoIcon {
    img{
      border-radius: 50%;
    }
  }

  .logoText {
    h2 {
      margin: 0 0 0.25rem 0;
      font-size: 0.75rem;
      font-weight: 500;
      color: var(--text-secondary);
      text-transform: uppercase;
      letter-spacing: 0.1em;
    }

    h1 {
      margin: 0 0 0.25rem 0;
      font-size: 1.375rem;
      font-weight: 700;
      color: var(--deep-navy);
      letter-spacing: -0.025em;
    }

    .roleIndicator {
      font-size: 0.7rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.1em;
      padding: 0.25rem 0.5rem;
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.8);
      border: 1px solid currentColor;
      display: inline-block;
      margin-top: 0.25rem;
    }
  }
}

.nav {
  flex: 1;
  padding: 1.5rem 0;
  
  ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
}

.navItem {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0;
  position: relative;
  margin: 0 1rem;
  border-radius: 12px;
  font-weight: 500;
  background: transparent;

  &:hover {
    background: linear-gradient(135deg, var(--background-light) 0%, rgba(20, 129, 185, 0.05) 100%);
    color: var(--corporate-blue);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(20, 129, 185, 0.1);

    .icon {
      color: var(--corporate-blue);
      transform: scale(1.1);
    }
  }

  &.active {
    background: linear-gradient(135deg, var(--corporate-blue) 0%, var(--blue-light) 100%);
    color: var(--pure-white);
    box-shadow: 0 4px 16px rgba(20, 129, 185, 0.4);
    transform: translateX(2px);

    .icon {
      color: var(--pure-white);
      transform: scale(1.05);
    }

    .label {
      font-weight: 600;
    }

    .activeIndicator {
      position: absolute;
      right: 12px;
      width: 6px;
      height: 6px;
      background: var(--pure-white);
      border-radius: 50%;
      box-shadow: 0 0 6px rgba(255, 255, 255, 0.6);
    }

    &:hover {
      transform: translateX(2px);
      background: linear-gradient(135deg, var(--blue-dark) 0%, var(--corporate-blue) 100%);
    }
  }
}

.icon {
  margin-right: 1rem;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-secondary);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.label {
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.4;
  transition: font-weight 0.25s ease;
}

.userInfo {
  padding: 1.5rem;
  border-top: 1px solid var(--border-gray);
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(135deg, var(--background-light) 0%, var(--pure-white) 100%);
  margin-top: auto;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--border-gray) 50%, transparent 100%);
  }
}

.avatar {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--corporate-blue) 0%, var(--sky-blue) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--pure-white);
  box-shadow: 0 4px 12px rgba(20, 129, 185, 0.3);
  flex-shrink: 0;
  transition: all 0.25s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(20, 129, 185, 0.4);
  }
}

.userDetails {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.125rem;

  .userName {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--deep-navy);
    line-height: 1.3;
  }

  .userRole {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

.logoutBtn {
  background: var(--background-light);
  border: 1px solid var(--border-gray);
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.625rem;
  border-radius: 10px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &:hover {
    color: var(--corporate-blue);
    background: var(--pure-white);
    border-color: var(--corporate-blue);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(20, 129, 185, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(20, 129, 185, 0.15);
  }
}

// Responsive design
@media (max-width: 768px) {
  .sidebar {
    width: 260px;
    
    .logo {
      padding: 1.5rem 1rem 1rem;
      
      .logoIcon {
        width: 40px;
        height: 40px;
      }
      
      .logoText h1 {
        font-size: 1.25rem;
      }
    }
    
    .navItem {
      margin: 0 0.75rem;
      padding: 0.875rem 1rem;
    }
    
    .userInfo {
      padding: 1rem;
    }
  }
}

// Focus states for accessibility
.navItem:focus-visible,
.logoutBtn:focus-visible {
  outline: 2px solid var(--corporate-blue);
  outline-offset: 2px;
}

// Animation for smooth transitions
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.navItem {
  animation: slideIn 0.3s ease-out;
}

// Add stagger effect for menu items
.nav ul li:nth-child(1) .navItem { animation-delay: 0.1s; }
.nav ul li:nth-child(2) .navItem { animation-delay: 0.15s; }
.nav ul li:nth-child(3) .navItem { animation-delay: 0.2s; }
.nav ul li:nth-child(4) .navItem { animation-delay: 0.25s; }
.nav ul li:nth-child(5) .navItem { animation-delay: 0.3s; }
.nav ul li:nth-child(6) .navItem { animation-delay: 0.35s; }