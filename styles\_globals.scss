@import "tailwindcss";

// @import "tw-animate-css";

// @custom-variant dark (&:is(.dark *));

@font-face {
  font-family: "inter";
  src: url("/fonts/inter.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

:root {
  /* Custom Brand Colors */
  --deep-navy: #121622;
  --corporate-blue: #1481b9;
  --sky-blue: #189dd6;
  --background-light: #f8fafc;
  --border-gray: #e2e8f0;
  --text-secondary: #64748b;
  --blue-dark: #0f6a94;
  --blue-light: #1e9bcc;
  --pure-white: #ffffff;

  /* Legacy color variables updated with new scheme */
  --head: var(--deep-navy);
  --parawhite: rgb(255, 255, 255, 0.7);
  --para: rgb(18, 22, 34, 0.7);
  --white: #ffffff;
  --black: var(--deep-navy);
  --radius: 0.625rem;

  /* Shadcn/ui theme colors updated with new palette */
  --background: #ffffff;
  --foreground: var(--deep-navy);
  --card: #ffffff;
  --card-foreground: var(--deep-navy);
  --popover: #ffffff;
  --popover-foreground: var(--deep-navy);
  --primary: var(--corporate-blue);
  --primary-foreground: #ffffff;
  --secondary: #f8fafc;
  --secondary-foreground: var(--deep-navy);
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: var(--sky-blue);
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: var(--corporate-blue);

  /* Chart colors derived from brand palette */
  --chart-1: var(--corporate-blue);
  --chart-2: var(--sky-blue);
  --chart-3: var(--deep-navy);
  --chart-4: #60a5fa;
  --chart-5: #3b82f6;

  /* Sidebar specific colors */
  --sidebar: #ffffff;
  --sidebar-foreground: var(--deep-navy);
  --sidebar-primary: var(--corporate-blue);
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: var(--sky-blue);
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: var(--corporate-blue);
}

html {
  scroll-behavior: smooth;
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
  scroll-snap-align: center;
}

body {
  background: var(--white);
  color: var(--black);
  font-family: "inter", sans-serif;
  font-optical-sizing: auto;
}

::-webkit-scrollbar {
  width: 5px;
  border-radius: 5rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: var(--lightblue);
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #b5b4b4;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #888;
}

.dark {
  /* Dark theme with brand colors */
  --background: var(--deep-navy);
  --foreground: #ffffff;
  --card: #1e293b;
  --card-foreground: #ffffff;
  --popover: #1e293b;
  --popover-foreground: #ffffff;
  --primary: var(--sky-blue);
  --primary-foreground: var(--deep-navy);
  --secondary: #334155;
  --secondary-foreground: #ffffff;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: var(--corporate-blue);
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.15);
  --ring: var(--sky-blue);

  /* Dark theme chart colors */
  --chart-1: var(--sky-blue);
  --chart-2: var(--corporate-blue);
  --chart-3: #60a5fa;
  --chart-4: #3b82f6;
  --chart-5: #1d4ed8;

  /* Dark theme sidebar */
  --sidebar: #1e293b;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: var(--sky-blue);
  --sidebar-primary-foreground: var(--deep-navy);
  --sidebar-accent: var(--corporate-blue);
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: var(--sky-blue);
}

input:focus,
textarea:focus {
  outline: none;
}

.carouselbtn,button,img,svg {
  cursor: pointer !important;
}

span {
  color: var(--para);
}

h1,
h2,
h3 {
  color: var(--head);
}

p {
  color: var(--para);
}

img,svg{
  transition: 0.2s all;
}

img:hover,svg:hover{
  transform: scale(1.01);
}

.whitebgButton {
  padding: 0.5rem 4rem;
  background-color: var(--white);
  align-self: center;
  cursor: pointer;
  z-index: 9;
}

.blackBtn {
  padding: 0.5rem 4rem;
  background-color: var(--black);
  color: var(--white);
  text-align: center;
  font-size: 0.9rem;
  align-self: center;
  cursor: pointer;
  z-index: 9;
  border-radius: 0.2rem;
}

.imgContainer{
  overflow: hidden;
}

.imgContainerr {
  width: 100%;
  height: 24rem;
  position: relative;
  overflow: hidden;

  & > div {
    position: absolute;
    z-index: 9;
    font-size: 0.9rem;
    background-color: var(--white);
    top: 1rem;
    left: 1rem;
    color: rgba(208, 2, 27, 1);
    padding: 0.25rem 0.5rem;
  }
}

// checkout cart styling
.cartContainer {
  margin-top: 5rem;
  padding: 2rem;
  background-color: var(--white);
  min-width: 30vw !important;
  max-width: 30vw !important;

  .title {
    font-size: 1.5rem;
  }
  .cartItems {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    & > div {
      display: grid;
      grid-template-columns: 25% 70%;
      gap: 1rem;
      .imgContainer {
        position: relative;
        width: 100%;
        height: 8rem;
      }

      .info {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        & > div {
          display: flex;
          justify-content: space-between;
          align-items: center;

          h3 {
            font-size: 1.1rem;
          }

          .quantityContainer {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border: 1px solid rgba(221, 219, 220, 1);
            padding: 0.2rem 1rem;
          }

          span {
            font-size: 0.9rem;
          }
        }

        svg {
          transform: scale(1.2);
        }
      }
    }
  }

  .cartCheckOutContainer {
    position: fixed;
    bottom: 4rem;
    right: 0;
    width: 30vw;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    box-shadow: 0px -4px 8px rgba(0, 0, 0, 0.08);
    background-color: var(--white);

    & > div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      p {
        color: var(--black);
        font-weight: 600;
      }

      button {
        width: 100%;
      }
    }

    span {
      font-size: 0.9rem;
    }
  }
}

// custom animations since tailwind css isnt working
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in {
  animation: slideInRight 0.5s ease-in-out forwards;
}

.animate-slide-out {
  animation: slideOutRight 0.5s ease-in-out forwards;
}
