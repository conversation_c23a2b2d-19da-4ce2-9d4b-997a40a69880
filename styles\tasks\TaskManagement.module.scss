// Task Management Page Styles
.taskManagement {
  margin-left: 300px;
  padding: 2rem;
  background-color: #f8fafc;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.headerContent {
  flex: 1;

  h1 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--deep-navy);
    margin: 0 0 0.5rem 0;
  }

  p {
    color: #64748b;
    margin: 0;
    font-size: 1rem;
  }
}

.headerActions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.addTaskButton {
  background: var(--corporate-blue);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 4px rgba(20, 129, 185, 0.2);

  &:hover {
    background: var(--sky-blue);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(20, 129, 185, 0.3);
  }

  &:active {
    transform: translateY(0);
  }

  .addIcon {
    font-size: 1rem;
    font-weight: bold;
  }
}

.refreshButton {
  background: white;
  color: var(--corporate-blue);
  border: 1px solid var(--corporate-blue);
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;

  &:hover {
    background: var(--corporate-blue);
    color: white;
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .refreshIcon {
    font-size: 1rem;
    transition: transform 0.3s ease;
  }

  &.refreshing .refreshIcon {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.statsSection {
  margin-bottom: 2rem;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .statHeader {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .statIcon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    font-weight: 600;
  }

  .statInfo {
    flex: 1;

    h3 {
      font-size: 1.75rem;
      font-weight: 700;
      color: var(--deep-navy);
      margin: 0 0 0.25rem 0;
    }

    p {
      color: #64748b;
      margin: 0;
      font-size: 0.875rem;
      font-weight: 500;
    }
  }

  &.pending .statIcon {
    background: #ef4444;
  }

  &.inProgress .statIcon {
    background: #f59e0b;
  }

  &.completed .statIcon {
    background: #10b981;
  }

  &.total .statIcon {
    background: var(--corporate-blue);
  }
}

.tasksSection {
  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h2 {
      color: var(--deep-navy);
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;
    }

    .taskCount {
      color: #64748b;
      font-size: 0.875rem;
      background: #f1f5f9;
      padding: 0.375rem 0.75rem;
      border-radius: 9999px;
      font-weight: 500;
    }
  }
}

.errorMessage {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  .errorIcon {
    font-size: 1.25rem;
  }

  .errorText {
    flex: 1;
    font-weight: 500;
  }

  .retryButton {
    background: #dc2626;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background 0.2s ease;

    &:hover {
      background: #b91c1c;
    }
  }
}

// Responsive design
@media (max-width: 1024px) {
  .taskManagement {
    margin-left: 0;
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .taskManagement {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .headerActions {
    justify-content: flex-end;
  }

  .headerContent h1 {
    font-size: 1.75rem;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .addTaskButton {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .headerActions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .refreshButton {
    width: 100%;
  }
}
