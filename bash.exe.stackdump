Stack trace:
Frame         Function      Args
0007FFFF9960  00021006116E (00021028DEE8, 000210272B3E, 0007FFFF9960, 0007FFFF8860) msys-2.0.dll+0x2116E
0007FFFF9960  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9960  0002100469F2 (00021028DF99, 0007FFFF9818, 0007FFFF9960, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9960  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFF9960  00021006A525 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF914060000 ntdll.dll
7FF913600000 KERNEL32.DLL
7FF911580000 KERNELBASE.dll
7FF9136E0000 USER32.dll
7FF911AF0000 win32u.dll
7FF9131A0000 GDI32.dll
7FF9113B0000 gdi32full.dll
7FF911300000 msvcp_win.dll
7FF9111B0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF913490000 advapi32.dll
7FF9133E0000 msvcrt.dll
7FF912C20000 sechost.dll
7FF913E80000 RPCRT4.dll
7FF9107B0000 CRYPTBASE.DLL
7FF911B20000 bcryptPrimitives.dll
7FF912CD0000 IMM32.DLL
