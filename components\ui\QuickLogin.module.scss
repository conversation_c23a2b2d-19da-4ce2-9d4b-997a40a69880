// components/ui/QuickLogin.module.scss

.quickLogin {
  background: var(--pure-white);
  border: 1px solid var(--border-gray);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 2px 8px rgba(18, 22, 34, 0.08);

  h3 {
    margin: 0 0 1rem 0;
    color: var(--deep-navy);
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-weight: 600;
    color: var(--deep-navy);
    font-size: 0.9rem;
  }

  input {
    padding: 0.75rem;
    border: 1px solid var(--border-gray);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.25s ease;

    &:focus {
      outline: none;
      border-color: var(--corporate-blue);
      box-shadow: 0 0 0 3px rgba(20, 129, 185, 0.1);
    }

    &:disabled {
      background: var(--background-light);
      cursor: not-allowed;
    }

    &::placeholder {
      color: var(--text-secondary);
    }
  }
}

.loginBtn {
  padding: 0.75rem 1.5rem;
  background: var(--corporate-blue);
  color: var(--pure-white);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.25s ease;

  &:hover:not(:disabled) {
    background: var(--blue-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(20, 129, 185, 0.3);
  }

  &:disabled {
    background: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }
}

.testCredentials {
  padding-top: 1rem;
  border-top: 1px solid var(--border-gray);

  h4 {
    margin: 0 0 0.5rem 0;
    color: var(--deep-navy);
    font-size: 1rem;
    font-weight: 600;
  }

  p {
    margin: 0.25rem 0;
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.4;
  }
}
