// Add Task Modal Styles
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modalContent {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 0 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 1.5rem;

  h2 {
    color: var(--deep-navy);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
  }

  .closeButton {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #f1f5f9;
      color: var(--deep-navy);
    }
  }
}

.taskForm {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;

  label {
    display: block;
    font-weight: 600;
    color: var(--deep-navy);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  input,
  textarea,
  select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
    color: var(--deep-navy);

    &:focus {
      outline: none;
      border-color: var(--corporate-blue);
      box-shadow: 0 0 0 3px rgba(20, 129, 185, 0.1);
    }

    &::placeholder {
      color: #94a3b8;
    }
  }

  textarea {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
  }

  .inputError {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);

    &:focus {
      border-color: #ef4444;
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }
  }

  .errorText {
    display: block;
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-weight: 500;
  }
}

.statusSelect {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;

  option {
    padding: 0.5rem;
  }
}

.modalActions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;

  button {
    min-width: 100px;
  }
}

.submitButton {
  background: var(--corporate-blue);
  
  &:hover {
    background: var(--sky-blue);
  }

  &:disabled {
    background: #94a3b8;
    cursor: not-allowed;
  }
}

// Responsive design
@media (max-width: 640px) {
  .modalOverlay {
    padding: 0.5rem;
  }

  .modalContent {
    max-width: 100%;
    margin: 0;
  }

  .modalHeader {
    padding: 1rem 1rem 0 1rem;
    margin-bottom: 1rem;

    h2 {
      font-size: 1.25rem;
    }
  }

  .taskForm {
    padding: 0 1rem 1rem 1rem;
  }

  .formGroup {
    margin-bottom: 1rem;
  }

  .modalActions {
    flex-direction: column-reverse;
    gap: 0.5rem;

    button {
      width: 100%;
    }
  }
}

// Animation for form validation
.formGroup input.inputError,
.formGroup textarea.inputError,
.formGroup select.inputError {
  animation: shake 0.3s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}
