.containerHero {
  min-height: 92vh;
  background: url("/assets/landingpage/section.webp") center/cover no-repeat
    fixed border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  position: relative;

  & > div {
    margin-left: 5rem;
    z-index: 9;
    display: flex;
    flex-direction: column;
    h2 {
      font-size: 3rem;
      font-weight: 700;
      color: var(--white);
    }
  }
}

// this is the mission css
.missionContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  padding: 6rem 2rem;
  margin: 2rem;
  background: url("/assets/landingpage/forest.webp") center/cover no-repeat fixed
    border-box;
  position: relative;

  h1,
  p {
    text-align: center;
    color: var(--white);
    z-index: 9;
  }

  h1 {
    font-size: 2rem;
  }
}

.containerHero::before,
.missionContainer::before {
  content: "";
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.3);
}
