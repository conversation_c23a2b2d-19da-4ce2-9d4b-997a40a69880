================================================================================
                    API FLOW & IMPLEMENTATION GUIDE
================================================================================

📋 TABLE OF CONTENTS
================================================================================
1. Complete SignIn API Flow Example
2. Step-by-Step Flow Breakdown
3. File-by-File Code Execution
4. Error Handling Flow
5. Success Flow with Store Updates
6. How to Implement New API Integration (Step-by-Step)

================================================================================
1. COMPLETE SIGNIN API FLOW EXAMPLE
================================================================================

🎯 SCENARIO: User clicks "Sign In" button with email and password

📱 STARTING POINT: components/credentials/SignIn.jsx
📊 ENDING POINT: User redirected to dashboard with stores initialized

🔄 COMPLETE FLOW OVERVIEW:
User Input → Form Validation → Store Action → API Call → Response Handling → Store Update → UI Update → Redirect

================================================================================
2. STEP-BY-STEP FLOW BREAKDOWN
================================================================================

STEP 1: USER INTERACTION
========================
📍 FILE: components/credentials/SignIn.jsx
📍 LINE: User clicks submit button
📍 ACTION: handleSubmit function is triggered

```javascript
const handleSubmit = async (e) => {
  e.preventDefault();
  // Form validation happens here
  // Then calls the login function
};
```

STEP 2: FORM VALIDATION
=======================
📍 FILE: components/credentials/SignIn.jsx
📍 LINES: 95-115 (form validation logic)
📍 ACTION: Client-side validation of email and password

```javascript
// Validate email format
const emailError = !formData.email.includes('@');
// Validate password length
const passwordError = formData.password.length < 6;
```

STEP 3: STORE ACTION CALL
=========================
📍 FILE: components/credentials/SignIn.jsx
📍 LINE: 120 (approximately)
📍 ACTION: Calls login function from auth store

```javascript
const result = await login({
  email: formData.email,
  password: formData.password,
  rememberMe: rememberMe,
});
```

STEP 4: STORE FUNCTION EXECUTION
================================
📍 FILE: store/common/authStore.js
📍 LINES: 15-50 (login function)
📍 ACTION: Store sets loading state and makes API call

```javascript
login: async (credentials) => {
  set({ loading: true, error: null });
  
  try {
    const response = await apiClient.post('/api/auth/login', {
      email: credentials.email,
      password: credentials.password,
    });
    // Handle response...
  } catch (error) {
    // Handle error...
  }
},
```

STEP 5: API CONFIGURATION
=========================
📍 FILE: store/common/apiConfig.js
📍 LINES: 10-15 (Axios instance creation)
📍 ACTION: Axios instance is used with pre-configured settings

```javascript
const apiClient = axios.create({
  baseURL: 'https://news-app-backend-lo3a.onrender.com',
  headers: {
    'x-authorization-token': AUTH_TOKEN,
    'Content-Type': 'application/json',
  },
  timeout: 30000,
});
```

STEP 6: REQUEST INTERCEPTOR
===========================
📍 FILE: store/common/apiConfig.js
📍 LINES: 17-28 (Request interceptor)
📍 ACTION: Adds authentication token to request headers

```javascript
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  }
);
```

STEP 7: HTTP REQUEST SENT
==========================
📍 NETWORK: HTTP POST request to backend
📍 URL: https://news-app-backend-lo3a.onrender.com/api/auth/login
📍 HEADERS: Content-Type, x-authorization-token, Authorization (if token exists)
📍 BODY: { email: "<EMAIL>", password: "password123" }

STEP 8: BACKEND PROCESSING
==========================
📍 LOCATION: Your backend server
📍 ACTION: Backend validates credentials and returns response
📍 SUCCESS RESPONSE: { user: {...}, token: "jwt-token" }
📍 ERROR RESPONSE: { message: "Invalid credentials" }

STEP 9: RESPONSE INTERCEPTOR
============================
📍 FILE: store/common/apiConfig.js
📍 LINES: 30-70 (Response interceptor)
📍 ACTION: Handles response or error before returning to store

```javascript
apiClient.interceptors.response.use(
  (response) => {
    console.log('✅ API Response Success:', response.config.url, response.status);
    return response;
  },
  (error) => {
    // Handle 401, 403, 500, network errors
    if (error.response?.status === 401) {
      // Auto logout and redirect
    }
    return Promise.reject(new Error(userFriendlyMessage));
  }
);
```

STEP 10: STORE RESPONSE HANDLING
================================
📍 FILE: store/common/authStore.js
📍 LINES: 25-45 (success/error handling in login function)
📍 ACTION: Updates store state based on response

SUCCESS PATH:
```javascript
const { user, token } = response.data;

// Store token
if (credentials.rememberMe) {
  localStorage.setItem('authToken', token);
} else {
  sessionStorage.setItem('authToken', token);
}

// Update store state
set({
  user: userWithRole,
  isAuthenticated: true,
  loading: false,
  error: null,
});

return { success: true, user: userWithRole };
```

ERROR PATH:
```javascript
set({
  loading: false,
  error: error.message,
});
return { success: false, error: error.message };
```

STEP 11: COMPONENT RESPONSE HANDLING
====================================
📍 FILE: components/credentials/SignIn.jsx
📍 LINES: 130-150 (result handling)
📍 ACTION: Component handles the returned result

SUCCESS PATH:
```javascript
if (result.success) {
  // Clear form
  setFormData({ email: '', password: '' });
  
  // Show success notification
  showSuccessMessage(`Welcome back, ${result.user.name}!`);
  
  // Initialize stores based on user role
  await initializeStores(result.user);
  
  // Redirect to dashboard
  router.push('/');
}
```

ERROR PATH:
```javascript
else {
  showErrorMessage(result.error || 'Login failed. Please try again.');
}
```

STEP 12: STORE INITIALIZATION
=============================
📍 FILE: store/index.js
📍 LINES: 78-121 (initializeStores function)
📍 ACTION: Initializes role-specific stores based on user role

```javascript
export const initializeStores = async (user) => {
  const stores = getStoresByRole(user.role);
  
  switch (user.role) {
    case USER_TYPES.ADMIN:
      await stores.dashboard.getState().getDashboardStats();
      break;
    case USER_TYPES.EMPLOYER:
      await stores.team.getState().getTeamMembers();
      break;
    case USER_TYPES.USER:
      await stores.profile.getState().getProfile();
      await stores.tasks.getState().getYourTasks();
      break;
  }
};
```

STEP 13: UI NOTIFICATION
========================
📍 FILE: store/common/uiStore.js
📍 LINES: 50-70 (addNotification function)
📍 ACTION: Shows success notification to user

```javascript
showSuccessMessage: (message, title = 'Success') => {
  return get().addNotification({
    type: 'success',
    title,
    message,
  });
},
```

STEP 14: NAVIGATION
===================
📍 FILE: components/credentials/SignIn.jsx
📍 LINE: router.push('/')
📍 ACTION: User is redirected to dashboard page

STEP 15: DASHBOARD LOAD
=======================
📍 FILE: app/page.jsx
📍 ACTION: Dashboard loads with user authenticated and stores initialized
📍 RESULT: User sees personalized dashboard based on their role

================================================================================
3. FILE-BY-FILE CODE EXECUTION
================================================================================

📁 components/credentials/SignIn.jsx
├── handleSubmit() called
├── Form validation executed
├── login() called from auth store
└── Response handling and redirect

📁 store/common/authStore.js
├── login() function executed
├── Loading state set to true
├── API call made via apiClient
├── Response processed
├── Store state updated
└── Result returned to component

📁 store/common/apiConfig.js
├── Request interceptor adds headers
├── HTTP request sent to backend
├── Response interceptor processes response
└── Final response/error returned

📁 store/common/uiStore.js
├── showSuccessMessage() called
├── Notification added to state
└── Auto-dismiss timer started

📁 store/index.js
├── initializeStores() called
├── Role-specific stores identified
├── Store initialization based on role
└── Additional data loaded

📁 app/page.jsx
├── Component re-renders
├── Authentication state checked
├── Role-based content displayed
└── Dashboard fully loaded

================================================================================
4. ERROR HANDLING FLOW
================================================================================

🚨 NETWORK ERROR SCENARIO:
User Input → Store Action → API Call → Network Failure → Response Interceptor → Error Transformation → Store Error State → Component Error Display

🚨 AUTHENTICATION ERROR SCENARIO:
User Input → Store Action → API Call → 401 Response → Response Interceptor → Auto Logout → Redirect to SignIn

🚨 VALIDATION ERROR SCENARIO:
User Input → Form Validation → Error Display (No API call made)

================================================================================
5. SUCCESS FLOW WITH STORE UPDATES
================================================================================

✅ COMPLETE SUCCESS FLOW:
User Input → Validation → API Call → Success Response → Token Storage → Store Update → Role Detection → Store Initialization → Notification → Redirect → Dashboard Load

✅ STORE STATE CHANGES:
1. loading: false → true (API call starts)
2. error: null (clear previous errors)
3. user: null → user object (on success)
4. isAuthenticated: false → true (on success)
5. loading: true → false (API call complete)

✅ UI STATE CHANGES:
1. Form shows loading state
2. Submit button disabled
3. Success notification appears
4. Form clears
5. Redirect to dashboard
6. Dashboard loads with user data

================================================================================
6. HOW TO IMPLEMENT NEW API INTEGRATION (STEP-BY-STEP)
================================================================================

🎯 EXAMPLE: Let's implement a "Create Task" API integration

STEP 1: IDENTIFY THE REQUIREMENTS
=================================
📋 WHAT YOU NEED TO KNOW:
- API endpoint URL (e.g., POST /api/tasks)
- Request payload structure (e.g., { name, description, assignedTo, dueDate })
- Response structure (e.g., { id, name, description, status, createdAt })
- Which user roles can access this API (admin, employer, user)
- Where in the UI this will be called (e.g., AddTaskModal component)

STEP 2: CHOOSE THE RIGHT STORE
==============================
📁 STORE SELECTION BASED ON USER ROLE:
- Admin functionality → store/admin/taskStore.js
- Employer functionality → store/employers/taskStore.js
- User functionality → store/users/taskStore.js
- Common functionality → store/common/ (if used by all roles)

🎯 FOR OUR EXAMPLE: We'll add to store/admin/taskStore.js

STEP 3: ADD THE API FUNCTION TO THE STORE
=========================================
📍 FILE: store/admin/taskStore.js (or appropriate store)
📍 LOCATION: Inside the create() function

```javascript
// Add this function to the store object
createTask: async (taskData) => {
  set({ loading: true, error: null });
  try {
    // Make API call using apiClient
    const response = await apiClient.post('/api/tasks', taskData);

    // Update store state with new task
    set((state) => ({
      tasks: [response.data, ...state.tasks],
      loading: false,
    }));

    // Recalculate statistics
    get().calculateStats();

    return { success: true, data: response.data };
  } catch (error) {
    set({ loading: false, error: error.message });
    console.error('Error creating task:', error);
    throw error;
  }
},
```

STEP 4: UPDATE STORE STATE STRUCTURE (IF NEEDED)
================================================
📍 FILE: Same store file
📍 ACTION: Add any new state properties needed

```javascript
// If you need new state properties, add them to the initial state
const useAdminTaskStore = create((set, get) => ({
  // Existing state...
  tasks: [],
  loading: false,
  error: null,

  // Add new state if needed
  taskCategories: [], // Example: if your API returns categories

  // Your new function here...
  createTask: async (taskData) => {
    // Implementation from Step 3
  },
}));
```

STEP 5: CREATE THE UI COMPONENT
===============================
📍 FILE: components/tasks/AddTaskModal.jsx (or appropriate component)
📍 ACTION: Create component that uses the store function

```javascript
// components/tasks/AddTaskModal.jsx
import { useState } from 'react';
import useAdminTaskStore from '@/store/admin/taskStore';
import useUIStore from '@/store/common/uiStore';

const AddTaskModal = () => {
  const { createTask, loading } = useAdminTaskStore();
  const { showSuccessMessage, showErrorMessage, closeModal } = useUIStore();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    assignedTo: '',
    dueDate: '',
  });

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const result = await createTask(formData);

      if (result.success) {
        showSuccessMessage('Task created successfully!');
        setFormData({ name: '', description: '', assignedTo: '', dueDate: '' });
        closeModal('addTask');
      }
    } catch (error) {
      showErrorMessage(error.message || 'Failed to create task');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields here */}
      <button type="submit" disabled={loading}>
        {loading ? 'Creating...' : 'Create Task'}
      </button>
    </form>
  );
};

export default AddTaskModal;
```

STEP 6: ADD FORM VALIDATION (OPTIONAL BUT RECOMMENDED)
======================================================
📍 FILE: Same component file
📍 ACTION: Add client-side validation

```javascript
const validateForm = () => {
  const errors = {};

  if (!formData.name.trim()) {
    errors.name = 'Task name is required';
  }

  if (!formData.description.trim()) {
    errors.description = 'Description is required';
  }

  if (!formData.dueDate) {
    errors.dueDate = 'Due date is required';
  }

  return errors;
};

// In handleSubmit, before API call:
const validationErrors = validateForm();
if (Object.keys(validationErrors).length > 0) {
  setErrors(validationErrors);
  return;
}
```

STEP 7: INTEGRATE COMPONENT INTO PARENT
=======================================
📍 FILE: Parent component (e.g., app/tasks-management/page.jsx)
📍 ACTION: Import and use the new component

```javascript
// app/tasks-management/page.jsx
import AddTaskModal from '@/components/tasks/AddTaskModal';
import useUIStore from '@/store/common/uiStore';

const TaskManagement = () => {
  const { modals, openModal } = useUIStore();

  return (
    <div>
      <button onClick={() => openModal('addTask')}>
        Add New Task
      </button>

      {modals.addTask && <AddTaskModal />}
    </div>
  );
};
```

STEP 8: TEST THE INTEGRATION
============================
📍 ACTION: Test the complete flow

1. **Start development server**: npm run dev
2. **Open browser**: Navigate to your component
3. **Test form submission**: Fill form and submit
4. **Check network tab**: Verify API request is sent correctly
5. **Check console**: Look for any errors
6. **Verify store update**: Check if data appears in UI
7. **Test error scenarios**: Try invalid data, network issues

STEP 9: ADD ERROR HANDLING ENHANCEMENTS
=======================================
📍 FILE: Your store file
📍 ACTION: Add specific error handling

```javascript
createTask: async (taskData) => {
  set({ loading: true, error: null });

  try {
    // Validate required fields
    if (!taskData.name || !taskData.description) {
      throw new Error('Name and description are required');
    }

    const response = await apiClient.post('/api/tasks', taskData);

    // Handle different response structures
    const newTask = response.data.task || response.data;

    set((state) => ({
      tasks: [newTask, ...state.tasks],
      loading: false,
    }));

    return { success: true, data: newTask };
  } catch (error) {
    // Enhanced error handling
    let errorMessage = 'Failed to create task';

    if (error.response?.status === 400) {
      errorMessage = 'Invalid task data provided';
    } else if (error.response?.status === 403) {
      errorMessage = 'You do not have permission to create tasks';
    } else if (error.message) {
      errorMessage = error.message;
    }

    set({ loading: false, error: errorMessage });
    throw new Error(errorMessage);
  }
},
```

STEP 10: ADD LOADING STATES TO UI
=================================
📍 FILE: Your component file
📍 ACTION: Show loading indicators

```javascript
return (
  <form onSubmit={handleSubmit}>
    {loading && <div className="loading-overlay">Creating task...</div>}

    <input
      type="text"
      value={formData.name}
      onChange={(e) => setFormData({...formData, name: e.target.value})}
      disabled={loading}
      placeholder="Task name"
    />

    <button type="submit" disabled={loading}>
      {loading ? (
        <>
          <span className="spinner"></span>
          Creating...
        </>
      ) : (
        'Create Task'
      )}
    </button>
  </form>
);
```

STEP 11: UPDATE STORE EXPORTS (IF NEEDED)
=========================================
📍 FILE: store/index.js
📍 ACTION: Make sure your store is exported

```javascript
// If you created a new store file, add it to exports
export { default as useNewFeatureStore } from './path/to/newFeatureStore';

// If you added to existing store, no changes needed
```

STEP 12: ADD TYPESCRIPT TYPES (OPTIONAL)
========================================
📍 FILE: types/api.ts (if using TypeScript)
📍 ACTION: Define types for better development experience

```typescript
export interface CreateTaskRequest {
  name: string;
  description: string;
  assignedTo?: string;
  dueDate: string;
  priority?: 'low' | 'medium' | 'high';
}

export interface TaskResponse {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in progress' | 'completed';
  createdAt: string;
  updatedAt: string;
}
```

================================================================================
                              SUMMARY CHECKLIST
================================================================================

✅ BEFORE YOU START:
□ Identify API endpoint and data structure
□ Determine which store to use based on user role
□ Plan the UI component structure

✅ IMPLEMENTATION STEPS:
□ Add API function to appropriate store
□ Update store state if needed
□ Create UI component with form
□ Add form validation
□ Integrate component into parent
□ Test the complete flow
□ Add error handling
□ Add loading states
□ Update exports if needed

✅ TESTING CHECKLIST:
□ API request sent correctly
□ Store state updates properly
□ UI shows loading states
□ Success notifications work
□ Error handling works
□ Form validation works
□ Component integrates properly

🎯 FOLLOW THIS PATTERN FOR ANY NEW API INTEGRATION!

This guide provides a complete template for implementing any new API integration
in your application. Simply replace the task creation example with your specific
API requirements and follow the same pattern.

Happy coding! 🚀✨
